#!/usr/bin/env python3
"""
测试动态距离阈值的精子轨迹跟踪
"""

import numpy as np
import math
import sys
import os

# 简化版本的动态距离计算函数（不依赖torch）
def calculate_dynamic_max_distance_simple(trajectory, base_distance=35, speed_multiplier=1.5):
    """
    根据精子的历史轨迹动态计算最大允许匹配距离（简化版本）
    """
    if len(trajectory) < 3:
        return base_distance

    # 计算最近几帧的平均速度
    recent_trajectory = trajectory[-min(5, len(trajectory)):]
    distances = []
    for i in range(1, len(recent_trajectory)):
        dx = recent_trajectory[i][0] - recent_trajectory[i-1][0]
        dy = recent_trajectory[i][1] - recent_trajectory[i-1][1]
        dist = math.sqrt(dx*dx + dy*dy)
        distances.append(dist)

    avg_speed = sum(distances) / len(distances) if distances else 0

    # 根据速度动态调整距离阈值
    if avg_speed > 15:  # 高速精子
        dynamic_distance = min(50, base_distance + avg_speed * speed_multiplier)
    elif avg_speed > 8:  # 中速精子
        dynamic_distance = min(42, base_distance + avg_speed * 0.8)
    else:  # 低速精子
        dynamic_distance = base_distance

    return float(dynamic_distance)

# 简化版本的运动一致性计算函数
def calculate_motion_consistency_simple(trajectory, curr_point):
    """
    计算运动方向一致性（简化版本）
    """
    if len(trajectory) < 3:
        return 1.0

    # 计算历史运动方向
    recent_trajectory = trajectory[-min(4, len(trajectory)):]
    if len(recent_trajectory) < 2:
        return 1.0

    # 计算历史平均运动方向
    historical_vectors = []
    for i in range(1, len(recent_trajectory)):
        dx = recent_trajectory[i][0] - recent_trajectory[i-1][0]
        dy = recent_trajectory[i][1] - recent_trajectory[i-1][1]
        if dx != 0 or dy != 0:
            historical_vectors.append([dx, dy])

    if not historical_vectors:
        return 1.0

    # 计算当前运动方向
    last_point = trajectory[-1]
    curr_dx = curr_point[0] - last_point[0]
    curr_dy = curr_point[1] - last_point[1]

    if curr_dx == 0 and curr_dy == 0:
        return 0.5

    # 计算与历史方向的一致性
    curr_norm = math.sqrt(curr_dx*curr_dx + curr_dy*curr_dy)
    if curr_norm == 0:
        return 0.5

    consistency_scores = []
    for hist_vector in historical_vectors:
        hist_norm = math.sqrt(hist_vector[0]*hist_vector[0] + hist_vector[1]*hist_vector[1])
        if hist_norm > 0:
            # 计算余弦相似度
            dot_product = curr_dx * hist_vector[0] + curr_dy * hist_vector[1]
            cosine_sim = dot_product / (curr_norm * hist_norm)
            consistency_scores.append(max(0, cosine_sim))

    if not consistency_scores:
        return 1.0

    avg_consistency = sum(consistency_scores) / len(consistency_scores)
    return avg_consistency

def test_dynamic_distance_calculation():
    """测试动态距离计算函数"""
    print("=== 测试动态距离计算 ===")
    
    # 测试案例1：慢速精子轨迹
    slow_trajectory = [[100, 100], [102, 101], [104, 102], [106, 103], [108, 104]]
    slow_distance = calculate_dynamic_max_distance(slow_trajectory)
    print(f"慢速精子 (平均速度约2.2): 动态距离阈值 = {slow_distance}")
    
    # 测试案例2：中速精子轨迹
    medium_trajectory = [[100, 100], [106, 105], [112, 110], [118, 115], [124, 120]]
    medium_distance = calculate_dynamic_max_distance(medium_trajectory)
    print(f"中速精子 (平均速度约7.8): 动态距离阈值 = {medium_distance}")
    
    # 测试案例3：高速精子轨迹
    fast_trajectory = [[100, 100], [120, 115], [140, 130], [160, 145], [180, 160]]
    fast_distance = calculate_dynamic_max_distance(fast_trajectory)
    print(f"高速精子 (平均速度约22.4): 动态距离阈值 = {fast_distance}")
    
    # 测试案例4：轨迹太短
    short_trajectory = [[100, 100], [102, 101]]
    short_distance = calculate_dynamic_max_distance(short_trajectory)
    print(f"短轨迹: 动态距离阈值 = {short_distance}")
    
    print()

def test_motion_consistency():
    """测试运动一致性计算函数"""
    print("=== 测试运动一致性计算 ===")
    
    # 测试案例1：一致的直线运动
    consistent_trajectory = [[100, 100], [110, 105], [120, 110], [130, 115]]
    next_point_consistent = [140, 120]  # 继续相同方向
    consistency1 = calculate_motion_consistency(consistent_trajectory, next_point_consistent)
    print(f"一致直线运动: 一致性分数 = {consistency1:.3f}")
    
    # 测试案例2：方向突然改变
    next_point_opposite = [120, 110]  # 反向运动
    consistency2 = calculate_motion_consistency(consistent_trajectory, next_point_opposite)
    print(f"方向突然改变: 一致性分数 = {consistency2:.3f}")
    
    # 测试案例3：轻微偏转
    next_point_slight = [135, 125]  # 轻微偏转
    consistency3 = calculate_motion_consistency(consistent_trajectory, next_point_slight)
    print(f"轻微偏转: 一致性分数 = {consistency3:.3f}")
    
    # 测试案例4：静止状态
    next_point_static = [130, 115]  # 静止
    consistency4 = calculate_motion_consistency(consistent_trajectory, next_point_static)
    print(f"静止状态: 一致性分数 = {consistency4:.3f}")
    
    # 测试案例5：轨迹太短
    short_trajectory = [[100, 100], [110, 105]]
    consistency5 = calculate_motion_consistency(short_trajectory, [120, 110])
    print(f"轨迹太短: 一致性分数 = {consistency5:.3f}")
    
    print()

def simulate_tracking_scenario():
    """模拟跟踪场景"""
    print("=== 模拟跟踪场景 ===")
    
    # 模拟两个相向运动的精子
    sperm1_trajectory = [[100, 100], [110, 105], [120, 110], [130, 115]]  # 向右上运动
    sperm2_trajectory = [[200, 120], [190, 115], [180, 110], [170, 105]]  # 向左下运动
    
    # 模拟它们相遇后的候选点
    candidate_point1 = [140, 120]  # sperm1的正常下一个位置
    candidate_point2 = [160, 100]  # sperm2的正常下一个位置
    
    # 计算sperm1对两个候选点的匹配分数
    print("精子1 (向右上运动):")
    distance1_1 = calculate_dynamic_max_distance(sperm1_trajectory)
    consistency1_1 = calculate_motion_consistency(sperm1_trajectory, candidate_point1)
    consistency1_2 = calculate_motion_consistency(sperm1_trajectory, candidate_point2)
    
    print(f"  动态距离阈值: {distance1_1}")
    print(f"  与候选点1的一致性: {consistency1_1:.3f}")
    print(f"  与候选点2的一致性: {consistency1_2:.3f}")
    
    # 计算sperm2对两个候选点的匹配分数
    print("精子2 (向左下运动):")
    distance2_1 = calculate_dynamic_max_distance(sperm2_trajectory)
    consistency2_1 = calculate_motion_consistency(sperm2_trajectory, candidate_point1)
    consistency2_2 = calculate_motion_consistency(sperm2_trajectory, candidate_point2)
    
    print(f"  动态距离阈值: {distance2_1}")
    print(f"  与候选点1的一致性: {consistency2_1:.3f}")
    print(f"  与候选点2的一致性: {consistency2_2:.3f}")
    
    print("\n分析结果:")
    print("- 精子1应该匹配候选点1 (一致性更高)")
    print("- 精子2应该匹配候选点2 (一致性更高)")
    print("- 这样可以避免轨迹互换")

def main():
    """主函数"""
    print("动态距离阈值精子跟踪测试")
    print("=" * 50)
    
    test_dynamic_distance_calculation()
    test_motion_consistency()
    simulate_tracking_scenario()
    
    print("\n总结:")
    print("1. 动态距离阈值根据精子速度自动调整")
    print("2. 高速精子获得更大的搜索范围")
    print("3. 运动一致性检查防止轨迹互换")
    print("4. 组合使用可以同时解决两个问题")

if __name__ == "__main__":
    main()
