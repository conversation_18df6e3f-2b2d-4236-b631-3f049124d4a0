# Ultralytics YOLO 🚀, GPL-3.0 license

# Parameters
nc: 2  # number of classes
depth_multiple: 0.33  # scales module repeats
width_multiple: 0.25  # scales convolution channels

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  [[-1, 1, Conv, [64, 3, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]], # 1-P2/4
   [-1, 3, C2f, [128, True]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C2f, [256, True]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 6, C2f, [512, True]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3TR, [1024, True]],
   [-1, 1, SPPF, [1024, 5]],
  ]  # 9

# YOLOv8.0n head
head:
  [[-1, 1, nn.<PERSON><PERSON><PERSON>, [None, 2, 'nearest']],
   [[-1, 6], 1, Con<PERSON>, [1]],  # cat backbone P4
   [-1, 3, C2f, [512]],  # 13

   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 4], 1, Concat, [1]],  # cat backbone P3
   [-1, 3, C2f, [256]],  # 17 (P3/8-small)

   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 12], 1, Concat, [1]],  # cat head P4
   [-1, 3, C2f, [512]],  # 20 (P4/16-medium)

   [-1, 1, Conv, [512, 3, 2]],
   [[-1, 9], 1, Concat, [1]],  # cat head P5
   [-1, 3, C3TR, [1024]],  # 23 (P5/32-large)

   [[15, 18, 21], 1, Detect_v8, [nc]],
  ]  # Detect(P3, P4, P5)

# head:
#   - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
#   - [[-1, 6], 1, Concat, [1]]  # cat backbone P4
#   - [-1, 3, C2f, [512]]  # 13

#   - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
#   - [[-1, 4], 1, Concat, [1]]  # cat backbone P3
#   - [-1, 3, C2f, [512]]  # 17 (P3/8-small)

#   - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
#   - [[-1, 2], 1, Concat, [1]]  # cat backbone P3
#   - [-1, 3, C2f, [256]]  # 20 (P3/8-small)

#   - [-1, 1, Conv, [256, 3, 2]]
#   - [[-1, 15], 1, Concat, [1]]  # cat head P4
#   - [-1, 3, C2f, [256]]  # 23 (P4/16-medium)

#   - [-1, 1, Conv, [256, 3, 2]]
#   - [[-1, 12], 1, Concat, [1]]  # cat head P4
#   - [-1, 3, C2f, [512]]  # 26 (P4/16-medium)

#   - [-1, 1, Conv, [512, 3, 2]]
#   - [[-1, 9], 1, Concat, [1]]  # cat head P5
#   - [-1, 3, C2f, [1024]]  # 29 (P5/32-large)

#   - [[18, 21, 24, 27], 1, Detect, [nc]]  # Detect(P3, P4, P5)