

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]

# # path: /home/<USER>/yolov5_obb-master/dataset/jyz_outer_boundary # dataset root dir
# train: /home/<USER>/yolov5_obb-master/dataset/jyz_outer_boundary/images #images   # train images (relative to 'path') 
# val: /home/<USER>/yolov5_obb-master/dataset/jyz_outer_boundary/images #images  # val images (relative to 'path') 
# # test: val/images  #images # test images (optional)

# # Classes
# nc: 1  # number of classes
# names: ['jyz_outer_boundary','jyz']  # class names




# path: /home/<USER>/yolov5_obb-master/dataset/DOTA   # dataset root dir
# train: train/images    # train images (relative to 'path') 
# val: val/images   # val images (relative to 'path') 
# test: test/images  # test images (optional)

path:  ./dataset/DOTA # dataset root dir
train:  train/images/train.txt #images   # train images (relative to 'path') 
val: val/images/val.txt #images  # val images (relative to 'path') 
test: val/images/val.txt  #images # test images (optional)

# Classes
# nc: 16  # number of classes
# names: ['plane', 'baseball-diamond', 'bridge', 'ground-track-field', 'small-vehicle', 
#         'large-vehicle', 'ship', 'tennis-court', 'basketball-court', 'storage-tank',  
#         'soccer-ball-field', 'roundabout', 'harbor', 'swimming-pool', 'helicopter']  # class names
nc: 16  # number of classes
names: ['plane', 'baseball-diamond', 'bridge', 'ground-track-field', 'small-vehicle', 
        'large-vehicle', 'ship', 'tennis-court', 'basketball-court', 'storage-tank',  
        'soccer-ball-field', 'roundabout', 'harbor', 'swimming-pool', 'helicopter', 
        'container-crane']  # class names

# Download script/URL (optional)
# download: https://ultralytics.com/assets/coco128.zip
