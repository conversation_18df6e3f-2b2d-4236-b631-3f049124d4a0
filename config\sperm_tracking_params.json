{"noise_std": 0.4, "distance_threshold": 15.0, "max_allowed_distance": 30.0, "unused_threshold": 8, "max_lost_frames": 25, "recovery_threshold": 0.6, "match_quality_threshold": 0.2, "overlap_threshold": 0.4, "near_weights": {"iou": 0.35, "particle": 0.4, "bhattacharyya": 0.35, "angle": 0.3, "rough_distance": 0.4, "speed": 0.3, "appearance": 0.4}, "far_weights": {"iou": 0.15, "particle": 0.5, "bhattacharyya": 0.4, "angle": 0.2, "rough_distance": 0.2, "speed": 0.2, "appearance": 0.5}, "im_velocity_threshold": 5.0, "pr_linearity_threshold": 0.45, "pr_velocity_threshold": 25.0, "morph_min_size": 6, "morph_max_size": 120, "morph_threshold": 20}