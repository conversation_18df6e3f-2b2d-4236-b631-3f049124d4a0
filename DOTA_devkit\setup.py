"""
    setup.py file for SWIG example
"""
from distutils.core import setup, Extension
import numpy

polyiou_module = Extension('_polyiou',
                           sources=['polyiou_wrap.cxx', 'polyiou.cpp'],
                           )
setup(name = 'polyiou',
      version = '0.1',
      author = "SWIG Docs",
      description = """Simple swig example from docs""",
      ext_modules = [polyiou_module],
      py_modules = ["polyiou"],
)


def draw(name, pred, i, image_folder):
    # 假设 pred 已经是 NumPy 数组或可以转换为 NumPy 数组的格式
    pred = np.array(pred) if not isinstance(pred, np.ndarray) else pred
    # 构建图像文件夹路径
    name_path = os.path.join(image_folder, name, "img1")

    #if not os.path.exists(name_path):
        #print(f"错误: 文件夹不存在: {name_path}")
        #return

    # 获取文件夹中所有文件的列表
    all_files = os.listdir(name_path)
    # 使用 tqdm 显示进度条
    for filename in tqdm(all_files, desc=f"Processing {name} frame {i}"):
        if filename.endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):  # 检查文件扩展名
            img_path = os.path.join(name_path, filename)  # 构建完整图像路径
            img = cv2.imread(img_path)  # 读取图像

            if img is not None:
                # 对每张图像进行绘制
                for s in pred:
                    p = np.round(s[:4]).astype(np.int32)
                    cv2.rectangle(img, (p[0], p[1]), (p[2], p[3]), (255, 0, 0), 3)
                    cv2.putText(img, str(int(round(s[4], 2) * 100)),
                                (p[0] + 20, p[1] + 20), cv2.FONT_HERSHEY_PLAIN, 2,
                                (0, 0, 255), thickness=3)
                # 保存绘制后的图像
                cv2.imwrite(f"debug/{name}_{i}_{filename}", img)
            else:
                print(f"警告: 无法读取图像: {img_path}")



def draw(video_name, pred, frame_id, image_folder):
    # 假设 pred 已经是 NumPy 数组或可以转换为 NumPy 数组的格式
    pred = np.array(pred) if not isinstance(pred, np.ndarray) else pred
    # 构建图像文件夹路径
    name_path = os.path.join(image_folder, video_name, "img1")

    if not os.path.exists(name_path):
        print(f"错误: 文件夹不存在: {name_path}")
        return

    # 获取文件夹中所有文件的列表
    all_files = os.listdir(name_path)
    # 使用 tqdm 显示进度条
    for filename in tqdm(all_files, desc=f"Processing {video_name} frame {frame_id}"):
        if filename.endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):  # 检查文件扩展名
            img_path = os.path.join(name_path, filename)  # 构建完整图像路径
            img = cv2.imread(img_path)  # 读取图像

            if img is not None:
                # 对每张图像进行绘制
                for s in pred:
                    p = np.round(s[:4]).astype(np.int32)
                    cv2.rectangle(img, (p[0], p[1]), (p[2], p[3]), (255, 0, 0), 3)
                    cv2.putText(img, str(int(round(s[4], 2) * 100)),
                                (p[0] + 20, p[1] + 20), cv2.FONT_HERSHEY_PLAIN, 2,
                                (0, 0, 255), thickness=3)
                # 保存绘制后的图像
                cv2.imwrite(f"debug/{video_name}_{frame_id}_{filename}", img)
            else:
                print(f"警告: 无法读取图像: {img_path}")
